<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Navegação Direta para Relatórios</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .sidebar-simulation {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-width: 300px;
        }
        .menu-item {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .menu-item:hover {
            background: #e9ecef;
        }
        .menu-item.reports {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .i-lucide-chart-spline {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #007bff;
            border-radius: 3px;
            margin-right: 8px;
        }
        .submenu {
            margin-left: 24px;
            padding: 4px 8px;
            font-size: 14px;
            color: #666;
            background: #f8f9fa;
            border-left: 2px solid #dee2e6;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste - Navegação Direta para Relatórios</h1>
        
        <div class="status info">
            <strong>Objetivo:</strong> Testar se o menu "Relatórios" navega diretamente para https://amvoxreport.aplopes.com/
        </div>

        <div class="status success">
            <strong>Comportamento Esperado:</strong>
            <ul>
                <li>✅ Clique em "Relatórios" deve navegar para a URL externa</li>
                <li>✅ Todos os submenus devem ser removidos</li>
                <li>✅ Não deve abrir modal</li>
            </ul>
        </div>

        <div class="status warning">
            <strong>⚠️ Atenção:</strong> O clique real irá navegar para a página externa. Use o botão "Simular Clique" para testar sem navegar.
        </div>

        <div>
            <button class="test-button" onclick="simulateClick()">🖱️ Simular Clique no Menu</button>
            <button class="test-button" onclick="addSubmenus()">➕ Adicionar Submenus</button>
            <button class="test-button" onclick="clearLog()">🧹 Limpar Log</button>
        </div>

        <!-- Simular estrutura do menu Relatórios do Chatwoot -->
        <div class="sidebar-simulation">
            <h3>📊 Menu Lateral (Simulação)</h3>
            
            <!-- Outros itens de menu -->
            <div class="menu-item">
                <span>📥</span>
                <span>Inbox</span>
            </div>
            
            <div class="menu-item">
                <span>💬</span>
                <span>Conversas</span>
            </div>
            
            <!-- Menu Relatórios - deve navegar diretamente -->
            <li class="menu-item reports" name="Reports" title="Relatórios" data-reports-main="true">
                <span class="i-lucide-chart-spline"></span>
                <span>Relatórios</span>
                <span class="i-lucide-chevron-up" style="margin-left: auto;">▼</span>
            </li>
            
            <!-- Submenus que devem ser removidos -->
            <div class="submenu" id="submenu-1">
                <a href="/app/accounts/1/reports/agents_overview">📊 Agentes</a>
            </div>
            
            <div class="submenu" id="submenu-2">
                <a href="/app/accounts/1/reports/labels_overview">🏷️ Etiquetas</a>
            </div>
            
            <div class="submenu" id="submenu-3">
                <a href="/app/accounts/1/reports/inboxes_overview">📥 Caixa de Entrada</a>
            </div>
            
            <div class="submenu" id="submenu-4">
                <a href="/app/accounts/1/reports/teams_overview">👥 Time</a>
            </div>
            
            <!-- Outros itens de menu -->
            <div class="menu-item">
                <span>⚙️</span>
                <span>Configurações</span>
            </div>
        </div>

        <div class="log" id="log"></div>
    </div>

    <!-- Carregar o script de modificação -->
    <script src="/modify-reports-menu-final.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('📝 Log limpo');
        }

        function simulateClick() {
            log('🖱️ Simulando clique no menu Relatórios...');
            
            const reportsMenu = document.querySelector('[name="Reports"]');
            if (reportsMenu) {
                // Interceptar navegação para não sair da página de teste
                const originalHref = window.location.href;
                
                // Simular clique
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                
                // Interceptar mudança de URL
                const originalLocationHref = window.location.href;
                Object.defineProperty(window.location, 'href', {
                    set: function(url) {
                        log(`🌐 Tentativa de navegação interceptada: ${url}`);
                        if (url === 'https://amvoxreport.aplopes.com/') {
                            log('✅ Sucesso! Menu configurado para navegar para URL correta');
                        } else {
                            log(`⚠️ URL inesperada: ${url}`);
                        }
                        // Não navegar realmente
                    },
                    get: function() {
                        return originalLocationHref;
                    }
                });
                
                reportsMenu.dispatchEvent(event);
                log('📊 Evento de clique disparado');
            } else {
                log('❌ Menu Relatórios não encontrado');
            }
        }

        function addSubmenus() {
            log('➕ Adicionando submenus dinamicamente...');
            
            const sidebar = document.querySelector('.sidebar-simulation');
            
            // Adicionar novos submenus
            const newSubmenu1 = document.createElement('div');
            newSubmenu1.className = 'submenu';
            newSubmenu1.innerHTML = '<a href="/reports/overview">📈 Visão Geral</a>';
            
            const newSubmenu2 = document.createElement('div');
            newSubmenu2.className = 'submenu';
            newSubmenu2.innerHTML = '<a href="/reports/csat">⭐ CSAT</a>';
            
            sidebar.appendChild(newSubmenu1);
            sidebar.appendChild(newSubmenu2);
            
            log('✅ Novos submenus adicionados - script deve removê-los');
        }

        // Log inicial
        log('🧪 Teste de navegação direta para relatórios iniciado');
        log('🎯 Objetivo: Clicar em Relatórios deve navegar para https://amvoxreport.aplopes.com/');
        
        // Verificar se o script foi carregado
        setTimeout(() => {
            if (window.modifyReportsMenu) {
                log('✅ Script de modificação detectado');
            } else {
                log('⚠️ Script de modificação não detectado');
            }
            
            if (window.removeAllReportsSubmenus) {
                log('✅ Função de remoção de submenus detectada');
            } else {
                log('⚠️ Função de remoção de submenus não detectada');
            }
        }, 1000);

        // Verificar se submenus foram removidos
        setInterval(() => {
            const visibleSubmenus = document.querySelectorAll('.submenu:not([style*="display: none"])');
            const hiddenSubmenus = document.querySelectorAll('.submenu[style*="display: none"]');
            
            if (hiddenSubmenus.length > 0 && visibleSubmenus.length === 0) {
                if (!document.body.hasAttribute('data-submenus-removed')) {
                    log(`✅ Sucesso! ${hiddenSubmenus.length} submenus foram removidos`);
                    document.body.setAttribute('data-submenus-removed', 'true');
                }
            } else if (visibleSubmenus.length > 0) {
                log(`⚠️ ${visibleSubmenus.length} submenus ainda visíveis`);
                document.body.removeAttribute('data-submenus-removed');
            }
        }, 3000);

        // Verificar se o menu foi modificado
        setInterval(() => {
            const reportsMenu = document.querySelector('[data-reports-modified="true"]');
            if (reportsMenu && !document.body.hasAttribute('data-menu-modified')) {
                log('✅ Menu Relatórios foi modificado pelo script');
                document.body.setAttribute('data-menu-modified', 'true');
            }
        }, 2000);
    </script>
</body>
</html>
