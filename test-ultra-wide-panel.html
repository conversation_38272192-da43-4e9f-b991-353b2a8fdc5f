<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Painel Ultra-Wide de Relatórios</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .sidebar-simulation {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-width: 300px;
            position: relative;
        }
        .menu-item {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .menu-item:hover {
            background: #e9ecef;
        }
        .menu-item.reports {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .i-lucide-chart-spline {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #007bff;
            border-radius: 3px;
            margin-right: 8px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .ultra-wide {
            background: #e7f3ff;
            color: #0066cc;
            border: 2px solid #0066cc;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .demo-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .size-comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .size-box {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        .size-small {
            background: #ffebee;
            color: #c62828;
            flex: 0 0 20%;
        }
        .size-medium {
            background: #fff3e0;
            color: #ef6c00;
            flex: 0 0 30%;
        }
        .size-large {
            background: #e8f5e8;
            color: #2e7d32;
            flex: 0 0 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste - Painel Ultra-Wide de Relatórios</h1>
        
        <div class="status ultra-wide">
            <strong>🚀 NOVO: Painel Ultra-Wide!</strong> Agora ocupa 80% da tela para máxima visualização
        </div>

        <div class="status success">
            <strong>Funcionalidades do Painel Ultra-Wide:</strong>
            <ul>
                <li>✅ <strong>80% da tela</strong> - Máximo espaço possível</li>
                <li>✅ <strong>Mínimo 800px</strong> - Garantia de usabilidade</li>
                <li>✅ <strong>Máximo 1600px</strong> - Otimizado para monitores grandes</li>
                <li>✅ <strong>Experiência desktop</strong> - Aproveita telas widescreen</li>
                <li>✅ <strong>Visualização completa</strong> - Dashboards e tabelas sem scroll</li>
                <li>✅ <strong>Multitarefa</strong> - Chatwoot ainda visível (20%)</li>
            </ul>
        </div>

        <div class="size-comparison">
            <div class="size-small">
                <div>Versão Original</div>
                <div>400px fixo</div>
                <div>~20% da tela</div>
            </div>
            <div class="size-medium">
                <div>Versão Expandida</div>
                <div>60% da tela</div>
                <div>600-1200px</div>
            </div>
            <div class="size-large">
                <div>🚀 Ultra-Wide</div>
                <div>80% da tela</div>
                <div>800-1600px</div>
            </div>
        </div>

        <div>
            <button class="test-button" onclick="openPanel()">📋 Abrir Painel Ultra-Wide</button>
            <button class="test-button" onclick="closePanel()">❌ Fechar Painel</button>
            <button class="test-button" onclick="simulateClick()">🖱️ Simular Clique no Menu</button>
            <button class="test-button" onclick="showSizeInfo()">📏 Mostrar Dimensões</button>
            <button class="test-button" onclick="clearLog()">🧹 Limpar Log</button>
        </div>

        <!-- Simular estrutura do menu Relatórios do Chatwoot -->
        <div class="sidebar-simulation">
            <h3>📊 Menu Lateral (Simulação)</h3>
            
            <!-- Menu Relatórios - deve abrir painel ultra-wide -->
            <li class="menu-item reports" name="Reports" title="Relatórios" data-reports-main="true">
                <span class="i-lucide-chart-spline"></span>
                <span>Relatórios</span>
                <span style="margin-left: auto; font-size: 10px; color: #666;">Ultra-Wide</span>
            </li>
            
            <!-- Outros itens de menu -->
            <div class="menu-item">
                <span>📥</span>
                <span>Inbox</span>
            </div>
            
            <div class="menu-item">
                <span>💬</span>
                <span>Conversas</span>
            </div>
            
            <div class="menu-item">
                <span>⚙️</span>
                <span>Configurações</span>
            </div>
        </div>

        <div class="demo-content">
            <h3>🎯 Painel Ultra-Wide (80% da tela):</h3>
            <ol>
                <li><strong>Máximo espaço</strong> → 80% da largura da tela</li>
                <li><strong>Telas pequenas</strong> → Mínimo 800px</li>
                <li><strong>Telas grandes</strong> → Máximo 1600px</li>
                <li><strong>Experiência premium</strong> → Dashboards completos visíveis</li>
                <li><strong>Multitarefa</strong> → Chatwoot ainda acessível (20%)</li>
            </ol>
            
            <h4>📏 Exemplos de Dimensões:</h4>
            <ul>
                <li><strong>Monitor 1366x768</strong> → 800px (mínimo garantido)</li>
                <li><strong>Monitor 1920x1080</strong> → 1536px (80% = excelente!)</li>
                <li><strong>Monitor 2560x1440</strong> → 1600px (máximo otimizado)</li>
                <li><strong>Monitor 3440x1440</strong> → 1600px (máximo para usabilidade)</li>
            </ul>
        </div>

        <div class="log" id="log"></div>
    </div>

    <!-- Carregar o script de modificação -->
    <script src="/modify-reports-menu-final.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('📝 Log limpo');
        }

        function openPanel() {
            log('📋 Abrindo painel ultra-wide manualmente...');
            if (window.openSidebarReportsPanel) {
                window.openSidebarReportsPanel();
                log('✅ Função openSidebarReportsPanel() executada');
                
                setTimeout(() => {
                    const panel = document.getElementById('sidebar-reports-panel');
                    if (panel) {
                        const width = panel.offsetWidth;
                        const screenWidth = window.innerWidth;
                        const percentage = Math.round((width / screenWidth) * 100);
                        log(`📏 Painel criado: ${width}px (${percentage}% da tela)`);
                    }
                }, 500);
            } else {
                log('❌ Função openSidebarReportsPanel() não encontrada');
            }
        }

        function closePanel() {
            log('❌ Fechando painel ultra-wide...');
            const panel = document.getElementById('sidebar-reports-panel');
            if (panel) {
                panel.style.transform = 'translateX(100%)';
                setTimeout(() => panel.remove(), 300);
                log('✅ Painel fechado');
            } else {
                log('⚠️ Nenhum painel aberto');
            }
        }

        function simulateClick() {
            log('🖱️ Simulando clique no menu Relatórios...');
            
            const reportsMenu = document.querySelector('[name="Reports"]');
            if (reportsMenu) {
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                
                reportsMenu.dispatchEvent(event);
                log('📊 Evento de clique disparado no menu');
            } else {
                log('❌ Menu Relatórios não encontrado');
            }
        }

        function showSizeInfo() {
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const expectedWidth = Math.min(Math.max(screenWidth * 0.8, 800), 1600);
            
            log(`📏 Informações da tela:`);
            log(`   Resolução: ${screenWidth}x${screenHeight}`);
            log(`   Painel esperado: ${expectedWidth}px (80% com limites)`);
            log(`   Chatwoot restante: ${screenWidth - expectedWidth}px (20%)`);
            
            if (expectedWidth === 800) {
                log(`   ⚠️ Usando largura mínima (tela pequena)`);
            } else if (expectedWidth === 1600) {
                log(`   ✅ Usando largura máxima (tela grande)`);
            } else {
                log(`   ✅ Usando 80% da tela (ideal)`);
            }
        }

        // Log inicial
        log('🧪 Teste de painel ultra-wide de relatórios iniciado');
        log('🎯 Objetivo: Painel com 80% da tela (800-1600px)');
        
        // Mostrar info da tela automaticamente
        setTimeout(showSizeInfo, 1000);
        
        // Verificar se o script foi carregado
        setTimeout(() => {
            if (window.openSidebarReportsPanel) {
                log('✅ Função openSidebarReportsPanel() detectada');
            } else {
                log('⚠️ Função openSidebarReportsPanel() não detectada');
            }
        }, 1500);

        // Detectar quando painel é aberto/fechado
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'sidebar-reports-panel') {
                            log('🎉 Painel ultra-wide foi criado!');
                            setTimeout(() => {
                                const width = node.offsetWidth;
                                const screenWidth = window.innerWidth;
                                const percentage = Math.round((width / screenWidth) * 100);
                                log(`📊 Dimensões finais: ${width}px (${percentage}% da tela)`);
                            }, 100);
                        }
                    });
                    mutation.removedNodes.forEach((node) => {
                        if (node.id === 'sidebar-reports-panel') {
                            log('👋 Painel ultra-wide foi removido');
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true
        });
    </script>
</body>
</html>
