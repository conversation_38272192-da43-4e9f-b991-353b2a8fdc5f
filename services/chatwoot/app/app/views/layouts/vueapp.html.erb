<!DOCTYPE html>
<html>
  <head>
    <title>
      <%= @global_config['INSTALLATION_NAME'] %>
    </title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=0"/>
    <% if @global_config['DISPLAY_MANIFEST'] %>
      <meta name="msapplication-TileColor" content="#1f93ff">
      <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
      <meta name="theme-color" content="#1f93ff">
      <meta name="description" content="Chatwoot is a customer support solution that helps companies engage customers over Messenger, Twitter, Telegram, WeChat, Whatsapp. Simply connect your channels and converse with your customers from a single place. Easily add new agents to your system and have them own and resolve conversations with customers.Chatwoot also gives you real-time reports to measure your team's performance, canned responses to easily respond to frequently asked questions and private notes for agents to collaborate among themselves.">
      <% if ENV['IOS_APP_IDENTIFIER'].present? %>
        <meta name="apple-itunes-app" content='app-id=<%= ENV['IOS_APP_IDENTIFIER'] %>'>
      <% end %>
      <link rel="apple-touch-icon" sizes="57x57" href="/apple-icon-57x57.png">
      <link rel="apple-touch-icon" sizes="60x60" href="/apple-icon-60x60.png">
      <link rel="apple-touch-icon" sizes="72x72" href="/apple-icon-72x72.png">
      <link rel="apple-touch-icon" sizes="76x76" href="/apple-icon-76x76.png">
      <link rel="apple-touch-icon" sizes="114x114" href="/apple-icon-114x114.png">
      <link rel="apple-touch-icon" sizes="120x120" href="/apple-icon-120x120.png">
      <link rel="apple-touch-icon" sizes="144x144" href="/apple-icon-144x144.png">
      <link rel="apple-touch-icon" sizes="152x152" href="/apple-icon-152x152.png">
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-icon-180x180.png">
      <link rel="icon" type="image/png" sizes="192x192"  href="/android-icon-192x192.png">
      <link class="favicon" rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
      <link class="favicon" rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png">
      <link class="favicon" rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
      <link rel="manifest" href="/manifest.json">
    <% end %>
    <link rel="icon" type="image/png" sizes="512x512" href="<%= @global_config['LOGO_THUMBNAIL'] %>">
    <%= csrf_meta_tags %>
    <script>
      window.chatwootConfig = {
        hostURL: '<%= ENV.fetch('FRONTEND_URL', '') %>',
        helpCenterURL: '<%= ENV.fetch('HELPCENTER_URL', '') %>',
        fbAppId: '<%= @global_config['FB_APP_ID'] %>',
        instagramAppId: '<%= @global_config['INSTAGRAM_APP_ID'] %>',
        googleOAuthClientId: '<%= ENV.fetch('GOOGLE_OAUTH_CLIENT_ID', nil) %>',
        googleOAuthCallbackUrl: '<%= ENV.fetch('GOOGLE_OAUTH_CALLBACK_URL', nil) %>',
        fbApiVersion: '<%= @global_config['FACEBOOK_API_VERSION'] %>',
        whatsappAppId: '<%= @global_config['WHATSAPP_APP_ID'] %>',
        whatsappConfigurationId: '<%= @global_config['WHATSAPP_CONFIGURATION_ID'] %>',
        whatsappApiVersion: '<%= @global_config['WHATSAPP_API_VERSION'] %>',
        signupEnabled: '<%= @global_config['ENABLE_ACCOUNT_SIGNUP'] %>',
        isEnterprise: '<%= @global_config['IS_ENTERPRISE'] %>',
        <% if @global_config['IS_ENTERPRISE'] %>
        enterprisePlanName: '<%= @global_config['INSTALLATION_PRICING_PLAN'] %>',
        <% end %>
        <% if @global_config['VAPID_PUBLIC_KEY'] %>
        vapidPublicKey: new Uint8Array(<%= Base64.urlsafe_decode64(@global_config['VAPID_PUBLIC_KEY']).bytes %>),
        <% end %>
        enabledLanguages: <%= available_locales_with_name.to_json.html_safe %>,
        helpUrls: <%= feature_help_urls.to_json.html_safe %>,
        selectedLocale: '<%= I18n.locale %>'
      }
      window.globalConfig = <%= raw @global_config.to_json %>
      window.browserConfig = {
        browser_name: '<%= browser.name %>',
      }
      window.errorLoggingConfig = '<%= ENV.fetch('SENTRY_FRONTEND_DSN', '') || ENV.fetch('SENTRY_DSN', '') %>'
    </script>
    <% if @global_config['ANALYTICS_TOKEN'].present? %>
    <script>
      window.analyticsConfig = {
        token: '<%= @global_config['ANALYTICS_TOKEN'] %>',
      }
    </script>
    <% end %>
    <%= vite_client_tag %>
    <%= vite_javascript_tag @application_pack %>
  </head>
  <body class="text-slate-600">
    <div id="app"></div>
    <noscript id="noscript">This app works best with JavaScript enabled.</noscript>
    <%= yield %>
    <% if @dashboard_scripts.present? %>
      <%= @dashboard_scripts.html_safe %>
    <% end %>
    <!-- Call Center Widget -->
    <% if user_signed_in? %>
      <script>
        window.CALL_CENTER_CONFIG = {
          url: "https://callcentermobile.ddns.net:4433/gdacx/gmag",
          title: "Call Center Amvox",
          enabled: true
        };
      </script>
      <script src="/hide-captain-menu.js" defer></script>
      <script src="/modify-reports-menu-final.js" defer></script>
      <script src="/simple-widget.js" defer></script>
      <script src="/hide-captain-menu.js" defer></script>
      <script src="/hide-help-center-menu.js" defer></script>
      <script src="/force-hide-help-center.js" defer></script>
    <% end %>
    <!-- End Call Center Widget -->
  </body>
</html>
