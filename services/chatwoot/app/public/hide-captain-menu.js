// Script para ocultar o menu "Capitão" do sidebar para todos os usuários (administrator e agent)
console.log('🔧 [v3.0] Ocultando menu "Capitão" do sidebar...');

function hideCaptainMenu() {
  console.log('🔍 Procurando menu "Capitão"...');

  let found = false;
  let hiddenCount = 0;

  // Método 1: Buscar por seletores específicos do Vue.js
  const captainSelectors = [
    'li[name="Captain"]',
    'div[name="Captain"]',
    '[title="Capitão"]',
    '[name="Captain"]',
    'a[href*="/captain/"]',
    'a[href*="captain"]'
  ];

  captainSelectors.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        const parentLi = el.closest('li');
        const targetElement = parentLi || el;

        if (targetElement && !targetElement.hasAttribute('data-captain-hidden')) {
          // Ocultar completamente
          targetElement.style.cssText = `
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
          `;
          targetElement.setAttribute('data-captain-hidden', 'true');

          console.log(`🎯 Menu Capitão ocultado via seletor: ${selector}`);
          found = true;
          hiddenCount++;
        }
      });
    } catch (e) {
      // Ignorar erros de seletores
    }
  });

  // Método 2: Buscar por texto específico em elementos de menu
  const textElements = document.querySelectorAll('span, div, a, li');
  textElements.forEach(el => {
    const text = el.textContent?.trim();
    if (text === 'Capitão' || text === 'Captain') {
      // Verificar se é realmente um item de menu
      const isInSidebar = el.closest('nav, aside, .sidebar, [role="navigation"]');
      if (isInSidebar) {
        const parentLi = el.closest('li');
        const targetElement = parentLi || el;

        if (targetElement && !targetElement.hasAttribute('data-captain-hidden')) {
          // Ocultar completamente
          targetElement.style.cssText = `
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
          `;
          targetElement.setAttribute('data-captain-hidden', 'true');

          console.log(`🎯 Menu Capitão ocultado via texto: "${text}"`);
          found = true;
          hiddenCount++;
        }
      }
    }
  });

  // Método 3: Buscar por ícone específico do Capitão
  const iconElements = document.querySelectorAll('.i-woot-captain, [class*="captain"]');
  iconElements.forEach(el => {
    const parentLi = el.closest('li');
    if (parentLi && !parentLi.hasAttribute('data-captain-hidden')) {
      parentLi.style.cssText = `
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
      `;
      parentLi.setAttribute('data-captain-hidden', 'true');

      console.log('🎯 Menu Capitão ocultado via ícone');
      found = true;
      hiddenCount++;
    }
  });

  if (found) {
    console.log(`✅ Menu "Capitão" ocultado com sucesso! (${hiddenCount} elementos)`);
  } else {
    console.log('⚠️ Menu "Capitão" não encontrado');
  }
}

// Função para observar mudanças no DOM
function observeCaptainMenu() {
  const observer = new MutationObserver((mutations) => {
    let shouldCheck = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Verificar se contém elementos relacionados ao menu Capitão
            if (node.querySelector && (
                node.querySelector('[name="Captain"]') ||
                node.querySelector('[title="Capitão"]') ||
                node.querySelector('.i-woot-captain') ||
                node.textContent?.includes('Capitão') ||
                node.textContent?.includes('Captain')
              )) {
              shouldCheck = true;
            }
          }
        });
      }
    });

    if (shouldCheck) {
      console.log('🔄 Mudanças detectadas, verificando menu Capitão...');
      setTimeout(hideCaptainMenu, 100);
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('👁️ Observer do menu Capitão ativado');
}

// Executar quando o DOM estiver pronto
function init() {
  console.log('🚀 Iniciando ocultação do menu Capitão...');

  // Executar imediatamente
  hideCaptainMenu();

  // Observar mudanças no DOM
  observeCaptainMenu();

  // Re-executar periodicamente para garantir
  setInterval(hideCaptainMenu, 5000);

  // Re-executar após carregamentos
  setTimeout(hideCaptainMenu, 1000);
  setTimeout(hideCaptainMenu, 3000);
  setTimeout(hideCaptainMenu, 7000);

  console.log('✅ Sistema de ocultação do menu Capitão ativado');
}

// Executar quando o DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}

// Executar também quando a página estiver completamente carregada
window.addEventListener('load', () => {
  setTimeout(init, 1000);
});

// Executar quando houver mudanças de rota (SPA)
window.addEventListener('popstate', () => {
  setTimeout(hideCaptainMenu, 500);
});

// Comando global para executar manualmente
window.hideCaptainMenu = hideCaptainMenu;

console.log('🔧 Script de ocultação do menu "Capitão" carregado!');
console.log('💡 Use hideCaptainMenu() para executar manualmente.');
