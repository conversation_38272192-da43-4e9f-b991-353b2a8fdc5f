#!/bin/bash
# Script para criar pacote completo de deploy

set -e

PACKAGE_NAME="n8n-host-customizations-$(date +%Y%m%d_%H%M%S)"
PACKAGE_DIR="deploy-packages"

echo "📦 Criando pacote de deploy: $PACKAGE_NAME"
echo "=============================================="

# Criar diretório de pacotes
mkdir -p "$PACKAGE_DIR"
cd "$PACKAGE_DIR"

# Criar estrutura do pacote
echo "📁 Criando estrutura..."
mkdir -p "$PACKAGE_NAME"/{customizations,services,scripts,docs,config,backups}

# Copiar arquivos essenciais
echo "📋 Copiando arquivos principais..."
cp ../docker-compose.yml "$PACKAGE_NAME/"
cp ../.env.example "$PACKAGE_NAME/"

# Copiar customizações
echo "🎨 Copiando customizações..."
if [ -d "../customizations" ]; then
    cp -r ../customizations/* "$PACKAGE_NAME/customizations/"
fi

# Copiar serviços (se existir)
echo "⚙️ Copiando serviços..."
if [ -d "../services" ]; then
    cp -r ../services/* "$PACKAGE_NAME/services/" 2>/dev/null || true
fi

# Copiar scripts
echo "🔧 Copiando scripts..."
cp ../apply-customizations.sh "$PACKAGE_NAME/scripts/" 2>/dev/null || true
cp ../backup-customizations.sh "$PACKAGE_NAME/scripts/" 2>/dev/null || true
cp ../consolidate-customizations.sh "$PACKAGE_NAME/scripts/" 2>/dev/null || true
cp ../extract_containers.sh "$PACKAGE_NAME/scripts/" 2>/dev/null || true

# Copiar documentação
echo "📚 Copiando documentação..."
cp ../*.md "$PACKAGE_NAME/docs/" 2>/dev/null || true

# Copiar configurações
echo "⚙️ Copiando configurações..."
if [ -d "../config" ]; then
    cp -r ../config/* "$PACKAGE_NAME/config/" 2>/dev/null || true
fi

# Criar arquivo de instalação
echo "📝 Criando guia de instalação..."
cat > "$PACKAGE_NAME/INSTALL.md" << 'EOF'
# 🚀 Instalação das Customizações n8n-host

## 📋 Conteúdo do Pacote
- **docker-compose.yml** - Configuração dos serviços
- **customizations/** - Todas as customizações do Chatwoot
- **services/** - Código fonte extraído (opcional)
- **scripts/** - Scripts de automação
- **docs/** - Documentação completa
- **config/** - Arquivos de configuração

## 🎯 Pré-requisitos
- Docker 20.10+
- Docker Compose 2.0+
- 4GB RAM mínimo
- 10GB espaço em disco

## ⚡ Instalação Rápida

### 1. Preparar Ambiente
```bash
# Extrair pacote
tar -xzf n8n-host-customizations-*.tar.gz
cd n8n-host-customizations-*

# Configurar variáveis
cp .env.example .env
nano .env  # Editar conforme necessário
```

### 2. Subir Serviços
```bash
# Iniciar todos os serviços
docker-compose up --build -d

# Verificar status
docker-compose ps
```

### 3. Validar Instalação
- **Chatwoot**: http://localhost:3000
- **N8N**: http://localhost:5678  
- **Evolution API**: http://localhost:8080

## 🔧 Aplicar em Ambiente Existente

Se você já tem um projeto n8n-host:

```bash
# Copiar customizações
cp -r customizations/ /caminho/para/seu/projeto/

# Aplicar customizações
cd /caminho/para/seu/projeto/
./scripts/apply-customizations.sh
```

## ✅ Funcionalidades Incluídas

### Chatwoot Customizado
- ✅ Menu Relatórios com iframe externo
- ✅ Remoção do menu Capitão
- ✅ Remoção do Help Center
- ✅ Widget Call Center integrado

### Integração Completa
- ✅ N8N para automação
- ✅ Evolution API para WhatsApp
- ✅ PostgreSQL + Redis
- ✅ Volumes persistentes

## 🆘 Suporte

### Logs
```bash
# Ver logs de todos os serviços
docker-compose logs

# Logs específicos
docker-compose logs chatwoot_app
docker-compose logs evolution
docker-compose logs n8n
```

### Problemas Comuns

**Porta em uso**
```bash
# Verificar portas
netstat -tulpn | grep :3000
# Alterar portas no .env se necessário
```

**Customizações não aplicadas**
```bash
# Aplicar manualmente
./scripts/apply-customizations.sh

# Verificar no navegador (F12 > Console)
# Deve aparecer: "🔧 Modificando menu Relatórios..."
```

**Serviços não sobem**
```bash
# Limpar e recriar
docker-compose down -v
docker system prune -f
docker-compose up --build
```

## 📞 Contato
Para suporte técnico, consulte a documentação em docs/ ou entre em contato com a equipe de desenvolvimento.

---
**Versão**: 1.0
**Data**: $(date +%Y-%m-%d)
**Compatibilidade**: Docker Compose 2.0+
EOF

# Criar script de instalação automática
echo "🤖 Criando script de instalação automática..."
cat > "$PACKAGE_NAME/install.sh" << 'EOF'
#!/bin/bash
# Script de instalação automática

set -e

echo "🚀 Instalação Automática n8n-host Customizations"
echo "================================================"

# Verificar pré-requisitos
echo "🔍 Verificando pré-requisitos..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker não encontrado. Instale o Docker primeiro."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose não encontrado. Instale o Docker Compose primeiro."
    exit 1
fi

echo "✅ Pré-requisitos OK"

# Configurar ambiente
echo "⚙️ Configurando ambiente..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "📝 Arquivo .env criado. Edite as configurações se necessário."
fi

# Verificar portas
echo "🔍 Verificando portas..."
PORTS=(3000 5678 8080 5432 6379)
for port in "${PORTS[@]}"; do
    if netstat -tulpn 2>/dev/null | grep -q ":$port "; then
        echo "⚠️ Porta $port em uso. Considere alterar no .env"
    fi
done

# Iniciar serviços
echo "🚀 Iniciando serviços..."
docker-compose up --build -d

# Aguardar inicialização
echo "⏳ Aguardando inicialização dos serviços..."
sleep 30

# Verificar status
echo "✅ Verificando status dos serviços..."
docker-compose ps

echo ""
echo "🎉 Instalação concluída!"
echo "======================="
echo ""
echo "🌐 Acessos:"
echo "- Chatwoot: http://localhost:3000"
echo "- N8N: http://localhost:5678"
echo "- Evolution API: http://localhost:8080"
echo ""
echo "📋 Para verificar logs:"
echo "docker-compose logs -f"
echo ""
echo "🔧 Para aplicar customizações manualmente:"
echo "./scripts/apply-customizations.sh"
EOF

chmod +x "$PACKAGE_NAME/install.sh"

# Tornar scripts executáveis
chmod +x "$PACKAGE_NAME/scripts/"*.sh 2>/dev/null || true

# Criar README do pacote
echo "📖 Criando README do pacote..."
cat > "$PACKAGE_NAME/README.md" << 'EOF'
# 📦 Pacote de Deploy n8n-host Customizations

Este pacote contém todas as customizações e configurações necessárias para deploy da stack n8n-host com Chatwoot customizado.

## 🎯 O que está incluído

### Serviços
- **N8N** - Automação de workflows
- **Evolution API** - Integração WhatsApp
- **Chatwoot** - Atendimento customizado
- **PostgreSQL** - Banco de dados
- **Redis** - Cache e filas

### Customizações Chatwoot
- Menu Relatórios com iframe externo
- Remoção do menu Capitão
- Remoção do Help Center  
- Widget Call Center integrado

## ⚡ Instalação Rápida

```bash
# Método 1: Instalação automática
./install.sh

# Método 2: Manual
cp .env.example .env
docker-compose up --build -d
```

## 📚 Documentação

Consulte os arquivos em `docs/` para documentação completa:
- `DOCUMENTACAO_COMPLETA_PROJETO.md` - Visão geral completa
- `GUIA_DEPLOY_COMPARTILHAMENTO.md` - Guia de deploy
- `INSTALL.md` - Instruções detalhadas

## 🆘 Suporte

Para problemas ou dúvidas, consulte a documentação ou verifique os logs:
```bash
docker-compose logs
```
EOF

# Criar arquivo de versão
echo "🏷️ Criando arquivo de versão..."
cat > "$PACKAGE_NAME/VERSION" << EOF
PACKAGE_NAME=$PACKAGE_NAME
VERSION=1.0
BUILD_DATE=$(date +%Y-%m-%d_%H:%M:%S)
GIT_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
CREATED_BY=$(whoami)
EOF

# Criar pacote compactado
echo "📦 Compactando pacote..."
tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_NAME"

# Criar checksum
echo "🔐 Criando checksum..."
sha256sum "${PACKAGE_NAME}.tar.gz" > "${PACKAGE_NAME}.tar.gz.sha256"

# Estatísticas do pacote
PACKAGE_SIZE=$(du -sh "${PACKAGE_NAME}.tar.gz" | cut -f1)
FILE_COUNT=$(find "$PACKAGE_NAME" -type f | wc -l)

echo ""
echo "🎉 Pacote criado com sucesso!"
echo "============================="
echo ""
echo "📦 Arquivo: $PACKAGE_DIR/${PACKAGE_NAME}.tar.gz"
echo "📏 Tamanho: $PACKAGE_SIZE"
echo "📄 Arquivos: $FILE_COUNT"
echo "🔐 Checksum: ${PACKAGE_NAME}.tar.gz.sha256"
echo ""
echo "🚀 Para usar:"
echo "1. Transferir arquivo para servidor de destino"
echo "2. Extrair: tar -xzf ${PACKAGE_NAME}.tar.gz"
echo "3. Instalar: cd ${PACKAGE_NAME} && ./install.sh"
echo ""
echo "✅ Pacote pronto para deploy!"

cd ..
