# 🚀 Guia de Deploy e Compartilhamento - n8n-host

## 📋 Resumo das Modificações Realizadas

### ✅ Customizações Implementadas
1. **Menu Relatórios Customizado** - Abre iframe com relatório externo
2. **Remoção Menu Capitão** - Menu completamente removido
3. **Remoção Help Center** - Central de ajuda removida
4. **Widget Call Center** - Widget integrado no layout
5. **Layout Personalizado** - Scripts carregados automaticamente

### 📁 Estrutura Criada
```
n8n-host/
├── customizations/           # ✅ Customizações organizadas
│   ├── chatwoot/
│   │   ├── scripts/         # 5 scripts JavaScript
│   │   ├── layouts/         # Layout customizado
│   │   └── config/          # Configurações
│   ├── README.md            # Documentação
│   └── INVENTORY.md         # Inventário detalhado
├── services/                # ✅ Código fonte extraído
│   ├── chatwoot/app/        # Código completo Chatwoot
│   ├── evolution-api/       # Código Evolution API
│   └── n8n/                # Customizações N8N
├── apply-customizations.sh  # ✅ Script de aplicação
├── backup-customizations.sh # ✅ Script de backup
├── docker-compose.yml       # ✅ Configuração atualizada
└── *.md                    # ✅ Documentação completa
```

## 🎯 Opções de Compartilhamento

### 1. 📤 Push para Repositório Git (Recomendado)

#### Verificar Status Atual
```bash
cd /home/<USER>/n8n-host
git status
git log --oneline -5
```

#### Fazer Push das Modificações
```bash
# Opção A: Push da branch atual
git push origin feature/chatwoot

# Opção B: Criar branch de release
git checkout -b release/chatwoot-v1.0
git push origin release/chatwoot-v1.0

# Opção C: Merge para main (se aprovado)
git checkout main
git merge feature/chatwoot
git push origin main
```

### 2. 📦 Criar Pacote de Deploy

#### Script de Empacotamento
```bash
#!/bin/bash
# create-deployment-package.sh

PACKAGE_NAME="n8n-host-customizations-$(date +%Y%m%d_%H%M%S)"
PACKAGE_DIR="deploy-packages"

echo "📦 Criando pacote de deploy: $PACKAGE_NAME"

mkdir -p "$PACKAGE_DIR"
cd "$PACKAGE_DIR"

# Criar estrutura do pacote
mkdir -p "$PACKAGE_NAME"/{customizations,services,scripts,docs,config}

# Copiar arquivos essenciais
cp ../docker-compose.yml "$PACKAGE_NAME/"
cp -r ../customizations/* "$PACKAGE_NAME/customizations/"
cp ../apply-customizations.sh "$PACKAGE_NAME/scripts/"
cp ../backup-customizations.sh "$PACKAGE_NAME/scripts/"
cp ../*.md "$PACKAGE_NAME/docs/"

# Criar arquivo de instalação
cat > "$PACKAGE_NAME/INSTALL.md" << 'EOF'
# 🚀 Instalação das Customizações

## Pré-requisitos
- Docker e Docker Compose instalados
- Git (opcional)

## Instalação Rápida
1. Extrair pacote: `tar -xzf pacote.tar.gz`
2. Entrar no diretório: `cd n8n-host-customizations-*`
3. Executar: `docker-compose up --build -d`

## Aplicar em Ambiente Existente
1. Copiar customizations/ para seu projeto
2. Executar: `./scripts/apply-customizations.sh`
EOF

# Criar pacote compactado
tar -czf "${PACKAGE_NAME}.tar.gz" "$PACKAGE_NAME"
echo "✅ Pacote criado: $PACKAGE_DIR/${PACKAGE_NAME}.tar.gz"
```

### 3. 🌐 Deploy em Novo Servidor

#### Método 1: Clone do Repositório
```bash
# No servidor de destino
git clone https://github.com/seu-usuario/n8n-host.git
cd n8n-host

# Usar branch específica
git checkout feature/chatwoot

# Configurar ambiente
cp .env.example .env
nano .env  # Editar configurações

# Subir serviços
docker-compose up --build -d
```

#### Método 2: Usar Pacote de Deploy
```bash
# Transferir e extrair pacote
scp n8n-host-customizations.tar.gz servidor:/opt/
ssh servidor
cd /opt
tar -xzf n8n-host-customizations.tar.gz
cd n8n-host-customizations-*

# Configurar e executar
cp .env.example .env
nano .env
docker-compose up --build -d
```

## 🔧 Configuração de Ambiente

### Variáveis Essenciais (.env)
```env
# === PORTAS EXTERNAS ===
N8N_PORT_EXTERNAL=5678
EVOLUTION_PORT_EXTERNAL=8080
CHATWOOT_PORT_EXTERNAL=3000
REDIS_PORT_EXTERNAL=6379
POSTGRES_PORT_EXTERNAL=5432
CHATWOOT_POSTGRES_PORT_EXTERNAL=5433

# === CHATWOOT ===
CHATWOOT_SECRET_KEY_BASE=sua_chave_super_secreta_aqui
CHATWOOT_FRONTEND_URL=http://localhost:3000
CHATWOOT_DEFAULT_LOCALE=pt_BR
CHATWOOT_FORCE_SSL=false
CHATWOOT_ENABLE_ACCOUNT_SIGNUP=false
CHATWOOT_REDIS_URL=redis://redis:6379
CHATWOOT_POSTGRES_USER=chatwoot
CHATWOOT_POSTGRES_PASSWORD=senha_segura
CHATWOOT_POSTGRES_DB=chatwoot_production

# === EVOLUTION API ===
AUTHENTICATION_API_KEY=sua_api_key_evolution
DATABASE_ENABLED=true
DATABASE_PROVIDER=postgresql
DATABASE_CONNECTION_URI=*****************************************/evolution
DATABASE_SAVE_DATA_INSTANCE=true
DATABASE_SAVE_DATA_NEW_MESSAGE=true

# === N8N ===
N8N_HOST=localhost
WEBHOOK_URL=http://localhost:5678/
GENERIC_TIMEZONE=America/Sao_Paulo
N8N_LOG_LEVEL=info

# === REDIS ===
REDIS_USER=default
REDIS_PASSWORD=senha_redis

# === POSTGRES ===
POSTGRES_USER=postgres
POSTGRES_PASSWORD=senha_postgres
POSTGRES_DB=n8n
```

## ✅ Validação do Deploy

### 1. Verificar Serviços
```bash
# Status dos containers
docker-compose ps

# Logs dos serviços
docker-compose logs chatwoot_app
docker-compose logs evolution
docker-compose logs n8n
```

### 2. Testar Funcionalidades

#### Chatwoot (http://localhost:3000)
- [ ] Login funciona
- [ ] Menu Relatórios abre modal com iframe
- [ ] Menu Capitão não aparece
- [ ] Help Center não aparece
- [ ] Widget Call Center visível

#### Evolution API (http://localhost:8080)
- [ ] API responde
- [ ] Documentação acessível

#### N8N (http://localhost:5678)
- [ ] Interface carrega
- [ ] Workflows funcionam

### 3. Debug de Problemas

#### Customizações não aplicadas
```bash
# Verificar se scripts estão no container
docker exec n8n_chatwoot_app ls -la /app/public/

# Aplicar manualmente
./apply-customizations.sh

# Verificar logs do navegador (F12 > Console)
```

#### Serviços não sobem
```bash
# Verificar logs
docker-compose logs

# Verificar portas em uso
netstat -tulpn | grep :3000

# Limpar e recriar
docker-compose down -v
docker-compose up --build
```

## 🔄 Processo de Atualização

### Em Ambiente Existente
```bash
# Fazer backup
./backup-customizations.sh

# Atualizar código
git pull origin feature/chatwoot

# Aplicar mudanças
docker-compose up --build -d

# Verificar funcionamento
```

### Rollback se Necessário
```bash
# Via Git
git checkout HEAD~1
docker-compose up --build -d

# Via backup
cp backups/docker-compose-*.backup docker-compose.yml
docker-compose up --build -d
```

## 📋 Checklist de Deploy

### Pré-Deploy
- [ ] Código commitado e versionado
- [ ] Documentação atualizada
- [ ] Backup do ambiente atual
- [ ] Variáveis de ambiente configuradas
- [ ] Portas disponíveis no servidor

### Deploy
- [ ] Código transferido/clonado
- [ ] Docker Compose executado
- [ ] Serviços iniciados corretamente
- [ ] Customizações aplicadas

### Pós-Deploy
- [ ] Funcionalidades testadas
- [ ] Logs verificados
- [ ] Performance monitorada
- [ ] Equipe treinada
- [ ] Documentação entregue

## 🆘 Suporte e Manutenção

### Contatos
- **Desenvolvedor**: Filipe Amorim
- **Repositório**: https://github.com/seu-usuario/n8n-host
- **Branch**: feature/chatwoot

### Logs Importantes
```bash
# Chatwoot
docker-compose logs -f chatwoot_app

# Console do navegador (F12)
# Procurar por mensagens das customizações:
# "🔧 Modificando menu Relatórios..."
# "✅ Menu Capitão removido"
```

### Comandos Úteis
```bash
# Reiniciar apenas Chatwoot
docker-compose restart chatwoot_app

# Recriar containers
docker-compose up --build --force-recreate

# Limpar tudo e recomeçar
docker-compose down -v
docker system prune -f
docker-compose up --build
```

---

**🎯 Resultado Final**: Sistema completo com Chatwoot customizado, Evolution API e N8N integrados, pronto para produção com todas as modificações documentadas e versionadas.

**📞 Próximo Passo**: Escolher método de deploy e executar seguindo este guia!
