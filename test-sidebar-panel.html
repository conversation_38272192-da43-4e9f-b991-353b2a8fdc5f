<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Painel Lateral de Relatórios</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .sidebar-simulation {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-width: 300px;
            position: relative;
        }
        .menu-item {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .menu-item:hover {
            background: #e9ecef;
        }
        .menu-item.reports {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .i-lucide-chart-spline {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #007bff;
            border-radius: 3px;
            margin-right: 8px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .demo-content {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste - Painel Lateral de Relatórios</h1>
        
        <div class="status info">
            <strong>Novo Comportamento:</strong> Clique em "Relatórios" abre um painel lateral na direita da tela
        </div>

        <div class="status success">
            <strong>Funcionalidades do Painel Expandido:</strong>
            <ul>
                <li>✅ Abre na lateral direita (60% da tela, min 600px, max 1200px)</li>
                <li>✅ Área de visualização ampliada para melhor experiência</li>
                <li>✅ Animação suave de entrada/saída</li>
                <li>✅ Iframe com a página de relatórios</li>
                <li>✅ Botão de fechar (X)</li>
                <li>✅ Fechar com tecla ESC</li>
                <li>✅ Loading indicator</li>
                <li>✅ Não navega para outra página</li>
                <li>✅ Design responsivo</li>
            </ul>
        </div>

        <div class="status warning">
            <strong>⚠️ Demonstração:</strong> Use os botões abaixo para testar o painel lateral
        </div>

        <div>
            <button class="test-button" onclick="openPanel()">📋 Abrir Painel Lateral</button>
            <button class="test-button" onclick="closePanel()">❌ Fechar Painel</button>
            <button class="test-button" onclick="simulateClick()">🖱️ Simular Clique no Menu</button>
            <button class="test-button" onclick="clearLog()">🧹 Limpar Log</button>
        </div>

        <!-- Simular estrutura do menu Relatórios do Chatwoot -->
        <div class="sidebar-simulation">
            <h3>📊 Menu Lateral (Simulação)</h3>
            
            <!-- Outros itens de menu -->
            <div class="menu-item">
                <span>📥</span>
                <span>Inbox</span>
            </div>
            
            <div class="menu-item">
                <span>💬</span>
                <span>Conversas</span>
            </div>
            
            <!-- Menu Relatórios - deve abrir painel lateral -->
            <li class="menu-item reports" name="Reports" title="Relatórios" data-reports-main="true">
                <span class="i-lucide-chart-spline"></span>
                <span>Relatórios</span>
            </li>
            
            <!-- Outros itens de menu -->
            <div class="menu-item">
                <span>⚙️</span>
                <span>Configurações</span>
            </div>
        </div>

        <div class="demo-content">
            <h3>🎯 Como Funciona (Painel Expandido):</h3>
            <ol>
                <li><strong>Clique em "Relatórios"</strong> → Abre painel lateral expandido na direita</li>
                <li><strong>Painel grande</strong> → 60% da tela (mínimo 600px, máximo 1200px)</li>
                <li><strong>Painel desliza</strong> → Animação suave de entrada</li>
                <li><strong>Iframe carrega</strong> → Página de relatórios com mais espaço</li>
                <li><strong>Fechar</strong> → Botão X ou tecla ESC</li>
                <li><strong>Toggle</strong> → Clicar novamente fecha o painel</li>
                <li><strong>Responsivo</strong> → Adapta-se ao tamanho da tela</li>
            </ol>

            <h4>📏 Dimensões:</h4>
            <ul>
                <li><strong>Tela pequena (< 1000px)</strong> → 600px de largura</li>
                <li><strong>Tela média (1000-2000px)</strong> → 60% da largura</li>
                <li><strong>Tela grande (> 2000px)</strong> → 1200px máximo</li>
            </ul>
        </div>

        <div class="log" id="log"></div>
    </div>

    <!-- Carregar o script de modificação -->
    <script src="/modify-reports-menu-final.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('📝 Log limpo');
        }

        function openPanel() {
            log('📋 Abrindo painel lateral manualmente...');
            if (window.openSidebarReportsPanel) {
                window.openSidebarReportsPanel();
                log('✅ Função openSidebarReportsPanel() executada');
            } else {
                log('❌ Função openSidebarReportsPanel() não encontrada');
            }
        }

        function closePanel() {
            log('❌ Fechando painel lateral...');
            const panel = document.getElementById('sidebar-reports-panel');
            if (panel) {
                panel.style.transform = 'translateX(100%)';
                setTimeout(() => panel.remove(), 300);
                log('✅ Painel fechado');
            } else {
                log('⚠️ Nenhum painel aberto');
            }
        }

        function simulateClick() {
            log('🖱️ Simulando clique no menu Relatórios...');
            
            const reportsMenu = document.querySelector('[name="Reports"]');
            if (reportsMenu) {
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                
                reportsMenu.dispatchEvent(event);
                log('📊 Evento de clique disparado no menu');
            } else {
                log('❌ Menu Relatórios não encontrado');
            }
        }

        // Log inicial
        log('🧪 Teste de painel lateral de relatórios iniciado');
        log('🎯 Objetivo: Abrir relatórios em painel lateral na direita');
        
        // Verificar se o script foi carregado
        setTimeout(() => {
            if (window.openSidebarReportsPanel) {
                log('✅ Função openSidebarReportsPanel() detectada');
            } else {
                log('⚠️ Função openSidebarReportsPanel() não detectada');
            }
            
            if (window.modifyReportsMenu) {
                log('✅ Script de modificação detectado');
            } else {
                log('⚠️ Script de modificação não detectado');
            }
        }, 1000);

        // Verificar se o menu foi modificado
        setInterval(() => {
            const reportsMenu = document.querySelector('[data-reports-modified="true"]');
            if (reportsMenu && !document.body.hasAttribute('data-menu-modified')) {
                log('✅ Menu Relatórios foi modificado pelo script');
                document.body.setAttribute('data-menu-modified', 'true');
            }
        }, 2000);

        // Detectar quando painel é aberto/fechado
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'sidebar-reports-panel') {
                            log('🎉 Painel lateral foi criado!');
                        }
                    });
                    mutation.removedNodes.forEach((node) => {
                        if (node.id === 'sidebar-reports-panel') {
                            log('👋 Painel lateral foi removido');
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true
        });
    </script>
</body>
</html>
