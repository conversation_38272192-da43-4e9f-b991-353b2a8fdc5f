<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Ocultar <PERSON></title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .sidebar-simulation {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            max-width: 300px;
        }
        .menu-item {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .menu-item:hover {
            background: #e9ecef;
        }
        .menu-item.captain {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .i-woot-captain {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #007bff;
            border-radius: 50%;
            margin-right: 8px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste - Ocultar Menu Capitão</h1>
        
        <div class="status info">
            <strong>Objetivo:</strong> Testar se o menu "Capitão" é ocultado automaticamente para todos os usuários
        </div>

        <div class="status success">
            <strong>Instruções:</strong> O script deve ocultar automaticamente todos os itens relacionados ao menu "Capitão" abaixo.
        </div>

        <div>
            <button class="test-button" onclick="simulateMenuLoad()">🔄 Simular Carregamento do Menu</button>
            <button class="test-button" onclick="showCaptainMenu()">👁️ Mostrar Menu Capitão</button>
            <button class="test-button" onclick="clearLog()">🧹 Limpar Log</button>
        </div>

        <!-- Simular estrutura do menu Capitão do Chatwoot -->
        <div class="sidebar-simulation">
            <h3>📋 Menu Lateral (Simulação)</h3>
            
            <!-- Outros itens de menu -->
            <div class="menu-item">
                <span>📥 Inbox</span>
            </div>
            
            <div class="menu-item">
                <span>💬 Conversas</span>
            </div>
            
            <!-- Menu Capitão - deve ser ocultado -->
            <li class="menu-item captain" name="Captain" title="Capitão">
                <span class="i-woot-captain"></span>
                <span>Capitão</span>
            </li>
            
            <!-- Submenu do Capitão -->
            <div class="menu-item captain" style="margin-left: 20px;">
                <a href="/app/accounts/1/captain/assistants">Assistentes</a>
            </div>
            
            <div class="menu-item captain" style="margin-left: 20px;">
                <a href="/app/accounts/1/captain/documents">Documentos</a>
            </div>
            
            <div class="menu-item captain" style="margin-left: 20px;">
                <a href="/app/accounts/1/captain/responses">FAQs</a>
            </div>
            
            <!-- Outros itens de menu -->
            <div class="menu-item">
                <span>📊 Relatórios</span>
            </div>
            
            <div class="menu-item">
                <span>⚙️ Configurações</span>
            </div>
        </div>

        <!-- Teste adicional com diferentes formatos -->
        <div class="sidebar-simulation">
            <h3>🔗 Teste de Diferentes Formatos</h3>
            
            <!-- Formato Vue.js -->
            <li class="grid gap-1 text-sm cursor-pointer select-none" name="Captain">
                <div class="flex items-center gap-2 px-2 py-1.5 rounded-lg h-8" title="Capitão">
                    <span class="i-woot-captain size-4"></span>
                    <span>Capitão</span>
                </div>
            </li>
            
            <!-- Formato com texto simples -->
            <div class="menu-item">Captain</div>
            <div class="menu-item">Capitão</div>
            
            <!-- Links diretos -->
            <a href="/captain/assistants" class="menu-item">Link Capitão 1</a>
            <a href="/app/accounts/1/captain/documents" class="menu-item">Link Capitão 2</a>
        </div>

        <div class="log" id="log"></div>
    </div>

    <!-- Carregar o script de ocultação -->
    <script src="/hide-captain-menu.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('📝 Log limpo');
        }

        function simulateMenuLoad() {
            log('🔄 Simulando carregamento dinâmico do menu...');
            
            // Criar novo elemento Capitão dinamicamente
            const newCaptainItem = document.createElement('div');
            newCaptainItem.className = 'menu-item captain';
            newCaptainItem.setAttribute('name', 'Captain');
            newCaptainItem.innerHTML = '<span class="i-woot-captain"></span><span>Capitão Dinâmico</span>';
            
            const sidebar = document.querySelector('.sidebar-simulation');
            sidebar.appendChild(newCaptainItem);
            
            log('✅ Novo item Capitão adicionado dinamicamente');
        }

        function showCaptainMenu() {
            log('👁️ Forçando exibição dos itens Capitão...');
            
            const captainItems = document.querySelectorAll('.captain, [name="Captain"], [title="Capitão"]');
            captainItems.forEach(item => {
                item.style.display = 'block';
                item.style.visibility = 'visible';
                item.style.opacity = '1';
                item.style.height = 'auto';
                item.removeAttribute('data-captain-hidden');
            });
            
            log(`📋 ${captainItems.length} itens Capitão forçados a aparecer`);
            log('⏳ Script deve ocultar novamente em breve...');
        }

        // Log inicial
        log('🧪 Teste de ocultação do menu Capitão iniciado');
        log('🎯 Objetivo: Ocultar todos os itens relacionados ao menu "Capitão"');
        
        // Verificar se o script foi carregado
        setTimeout(() => {
            if (window.hideCaptainMenu) {
                log('✅ Script de ocultação detectado');
            } else {
                log('⚠️ Script de ocultação não detectado');
            }
        }, 1000);

        // Verificar se itens foram ocultados
        setInterval(() => {
            const visibleCaptainItems = document.querySelectorAll('.captain:not([data-captain-hidden]), [name="Captain"]:not([data-captain-hidden])');
            const hiddenCaptainItems = document.querySelectorAll('[data-captain-hidden]');
            
            if (hiddenCaptainItems.length > 0 && visibleCaptainItems.length === 0) {
                if (!document.body.hasAttribute('data-success-logged')) {
                    log(`✅ Sucesso! ${hiddenCaptainItems.length} itens Capitão foram ocultados`);
                    document.body.setAttribute('data-success-logged', 'true');
                }
            } else if (visibleCaptainItems.length > 0) {
                log(`⚠️ ${visibleCaptainItems.length} itens Capitão ainda visíveis`);
                document.body.removeAttribute('data-success-logged');
            }
        }, 3000);
    </script>
</body>
</html>
