<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste - Painel Maximizado de Relatórios</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            display: flex;
            height: 100vh;
        }
        .sidebar-demo {
            width: 200px;
            background: #2d3748;
            color: white;
            padding: 20px;
            box-sizing: border-box;
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
        }
        .main-content {
            margin-left: 200px;
            flex: 1;
            padding: 20px;
            background: white;
            overflow-y: auto;
        }
        .menu-item {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
            color: #e2e8f0;
        }
        .menu-item:hover {
            background: #4a5568;
        }
        .menu-item.reports {
            background: #3182ce;
            color: white;
        }
        .i-lucide-chart-spline {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #60a5fa;
            border-radius: 3px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .maximized {
            background: #dcfce7;
            color: #166534;
            border: 2px solid #22c55e;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-weight: 500;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .demo-visual {
            display: flex;
            height: 200px;
            margin: 20px 0;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .demo-sidebar {
            width: 200px;
            background: #374151;
            color: white;
            padding: 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 14px;
        }
        .demo-panel {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
        }
        .size-info {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- Simular sidebar do Chatwoot -->
    <div class="sidebar-demo">
        <h3 style="margin-top: 0; font-size: 16px;">📱 Chatwoot</h3>
        
        <div class="menu-item">
            <span>📥</span>
            <span>Inbox</span>
        </div>
        
        <div class="menu-item">
            <span>💬</span>
            <span>Conversas</span>
        </div>
        
        <!-- Menu Relatórios - deve abrir painel maximizado -->
        <li class="menu-item reports" name="Reports" title="Relatórios" data-reports-main="true">
            <span class="i-lucide-chart-spline"></span>
            <span>Relatórios</span>
        </li>
        
        <div class="menu-item">
            <span>⚙️</span>
            <span>Config</span>
        </div>
        
        <div style="margin-top: auto; font-size: 12px; color: #9ca3af;">
            Sidebar: 200px
        </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="main-content">
        <h1>🧪 Teste - Painel Maximizado de Relatórios</h1>
        
        <div class="status maximized">
            <strong>🚀 PAINEL MAXIMIZADO!</strong> Ocupa toda a área disponível (100vw - 200px)
        </div>

        <div class="status info">
            <strong>Características do Painel Maximizado:</strong>
            <ul>
                <li>✅ <strong>Largura total</strong> - Toda a tela menos o sidebar (200px)</li>
                <li>✅ <strong>Posicionamento absoluto</strong> - left: 200px, right: 0</li>
                <li>✅ <strong>Altura completa</strong> - 100vh (tela inteira)</li>
                <li>✅ <strong>Sem margens</strong> - Aproveitamento máximo</li>
                <li>✅ <strong>Encosta no sidebar</strong> - Sem espaços desperdiçados</li>
                <li>✅ <strong>Responsivo</strong> - Adapta-se a qualquer resolução</li>
            </ul>
        </div>

        <div class="demo-visual">
            <div class="demo-sidebar">
                <div>Sidebar</div>
                <div>Chatwoot</div>
                <div style="font-size: 12px; margin-top: 10px;">200px</div>
            </div>
            <div class="demo-panel">
                <div>🚀 PAINEL MAXIMIZADO</div>
                <div style="font-size: 14px; margin-top: 10px;">calc(100vw - 200px)</div>
                <div style="font-size: 12px; margin-top: 5px;">Toda a área restante</div>
            </div>
        </div>

        <div class="size-info" id="sizeInfo">
            <strong>📏 Dimensões Calculadas:</strong><br>
            <span id="dimensions">Calculando...</span>
        </div>

        <div>
            <button class="test-button" onclick="openPanel()">📋 Abrir Painel Maximizado</button>
            <button class="test-button" onclick="closePanel()">❌ Fechar Painel</button>
            <button class="test-button" onclick="simulateClick()">🖱️ Simular Clique no Menu</button>
            <button class="test-button" onclick="updateDimensions()">📏 Atualizar Dimensões</button>
            <button class="test-button" onclick="clearLog()">🧹 Limpar Log</button>
        </div>

        <div class="log" id="log"></div>
    </div>

    <!-- Carregar o script de modificação -->
    <script src="/modify-reports-menu-final.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('📝 Log limpo');
        }

        function updateDimensions() {
            const screenWidth = window.innerWidth;
            const screenHeight = window.innerHeight;
            const sidebarWidth = 200;
            const panelWidth = screenWidth - sidebarWidth;
            
            const dimensionsEl = document.getElementById('dimensions');
            dimensionsEl.innerHTML = `
                Tela: ${screenWidth}x${screenHeight}<br>
                Sidebar: ${sidebarWidth}px<br>
                Painel: ${panelWidth}px x ${screenHeight}px<br>
                Aproveitamento: ${Math.round((panelWidth/screenWidth)*100)}% da largura
            `;
            
            log(`📏 Dimensões atualizadas: Painel ${panelWidth}x${screenHeight}`);
        }

        function openPanel() {
            log('📋 Abrindo painel maximizado manualmente...');
            if (window.openSidebarReportsPanel) {
                window.openSidebarReportsPanel();
                log('✅ Função openSidebarReportsPanel() executada');
                
                setTimeout(() => {
                    const panel = document.getElementById('sidebar-reports-panel');
                    if (panel) {
                        const width = panel.offsetWidth;
                        const height = panel.offsetHeight;
                        log(`📏 Painel criado: ${width}x${height}px`);
                        log(`✅ Painel maximizado ativo!`);
                    }
                }, 500);
            } else {
                log('❌ Função openSidebarReportsPanel() não encontrada');
            }
        }

        function closePanel() {
            log('❌ Fechando painel maximizado...');
            const panel = document.getElementById('sidebar-reports-panel');
            if (panel) {
                panel.style.transform = 'translateX(100%)';
                setTimeout(() => panel.remove(), 300);
                log('✅ Painel fechado');
            } else {
                log('⚠️ Nenhum painel aberto');
            }
        }

        function simulateClick() {
            log('🖱️ Simulando clique no menu Relatórios...');
            
            const reportsMenu = document.querySelector('[name="Reports"]');
            if (reportsMenu) {
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                
                reportsMenu.dispatchEvent(event);
                log('📊 Evento de clique disparado no menu');
            } else {
                log('❌ Menu Relatórios não encontrado');
            }
        }

        // Inicialização
        updateDimensions();
        
        // Atualizar dimensões quando a janela redimensionar
        window.addEventListener('resize', updateDimensions);
        
        // Log inicial
        log('🧪 Teste de painel maximizado de relatórios iniciado');
        log('🎯 Objetivo: Painel ocupando toda área (100vw - 200px)');
        
        // Verificar se o script foi carregado
        setTimeout(() => {
            if (window.openSidebarReportsPanel) {
                log('✅ Função openSidebarReportsPanel() detectada');
            } else {
                log('⚠️ Função openSidebarReportsPanel() não detectada');
            }
        }, 1000);

        // Detectar quando painel é aberto/fechado
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'sidebar-reports-panel') {
                            log('🎉 Painel maximizado foi criado!');
                            setTimeout(() => {
                                const width = node.offsetWidth;
                                const height = node.offsetHeight;
                                const expectedWidth = window.innerWidth - 200;
                                log(`📊 Dimensões: ${width}x${height}px`);
                                log(`📏 Esperado: ${expectedWidth}x${window.innerHeight}px`);
                                
                                if (Math.abs(width - expectedWidth) < 10) {
                                    log('✅ Painel maximizado corretamente!');
                                } else {
                                    log('⚠️ Dimensões não coincidem com o esperado');
                                }
                            }, 100);
                        }
                    });
                    mutation.removedNodes.forEach((node) => {
                        if (node.id === 'sidebar-reports-panel') {
                            log('👋 Painel maximizado foi removido');
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true
        });
    </script>
</body>
</html>
