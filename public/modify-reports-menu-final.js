// Script para modificar o menu "Relatórios" do sidebar
console.log('🔧 Modificando menu "Relatórios" do sidebar...');

function modifyReportsMenu() {
  // Possíveis seletores para o menu Relatórios
  const selectors = [
    'a[href*="reports"]',
    'a[href*="/reports"]',
    'a[href*="report"]',
    '[data-testid*="reports"]',
    '[data-testid*="report"]',
    'li:contains("Relatórios")',
    'li:contains("Reports")',
    '.sidebar-item:contains("Relatórios")',
    '.sidebar-item:contains("Reports")',
    '.menu-item:contains("Relatórios")',
    '.menu-item:contains("Reports")',
    '.i-lucide-chart-spline',
    '[class*="chart-spline"]'
  ];
  
  let found = false;
  
  // Tentar cada seletor
  selectors.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        elements.forEach((el, index) => {
          // Verificar se é realmente o menu de relatórios
          const isReportsMenu = isReportsElement(el);
          
          if (isReportsMenu) {
            console.log(`🎯 Encontrado menu Relatórios ${index + 1} com seletor: ${selector}`);
            console.log('Elemento:', el);
            console.log('Texto:', el.textContent?.trim());
            console.log('Href:', el.href);
            
            // Modificar o comportamento do clique
            modifyClickBehavior(el);
            found = true;
          }
        });
      }
    } catch (e) {
      // Ignorar erros de seletores inválidos
    }
  });
  
  // Buscar por texto específico
  const allElements = document.querySelectorAll('a, li, div, span, button');
  allElements.forEach(el => {
    const text = el.textContent?.trim().toLowerCase();
    if ((text === 'relatórios' || text === 'reports') && isInSidebar(el)) {
      console.log('🎯 Encontrado por texto:', el);
      console.log('Texto completo:', el.textContent);
      
      // Encontrar elemento clicável
      const clickableElement = findClickableParent(el);
      if (clickableElement) {
        modifyClickBehavior(clickableElement);
        found = true;
      }
    }
  });
  
  if (found) {
    console.log('✅ Menu "Relatórios" modificado com sucesso!');
  } else {
    console.log('⚠️ Menu "Relatórios" não encontrado');
  }
}

function isReportsElement(element) {
  const text = element.textContent?.toLowerCase() || '';
  const href = element.href?.toLowerCase() || '';
  const classes = element.className?.toLowerCase() || '';
  
  return (
    text.includes('relatórios') ||
    text.includes('reports') ||
    href.includes('reports') ||
    href.includes('report') ||
    classes.includes('chart') ||
    classes.includes('report')
  );
}

function isInSidebar(element) {
  const sidebar = element.closest('nav, .sidebar, .navigation, [role="navigation"]');
  return !!sidebar;
}

function findClickableParent(element) {
  let current = element;
  
  while (current && current !== document.body) {
    if (current.tagName === 'A' || 
        current.tagName === 'BUTTON' || 
        current.getAttribute('role') === 'button' ||
        current.style.cursor === 'pointer' ||
        current.onclick ||
        current.getAttribute('href')) {
      return current;
    }
    current = current.parentElement;
  }
  
  return element;
}

function modifyClickBehavior(element) {
  console.log('🔗 Modificando comportamento de clique do elemento:', element);
  
  // Verificar se já foi modificado
  if (element.getAttribute('data-reports-modified') === 'true') {
    console.log('ℹ️ Elemento já foi modificado, pulando...');
    return;
  }
  
  // Salvar comportamento original
  const originalHref = element.getAttribute('href');
  const originalOnClick = element.onclick;
  
  // Remover href se for link
  if (element.tagName === 'A') {
    element.removeAttribute('href');
  }
  
  // Remover onclick original
  element.onclick = null;
  
  // Adicionar cursor pointer
  element.style.cursor = 'pointer';
  
  // Clonar elemento para remover todos os event listeners
  const newElement = element.cloneNode(true);
  element.parentNode.replaceChild(newElement, element);
  
  // Adicionar novo comportamento de clique
  newElement.addEventListener('click', function(e) {
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    console.log('📊 Clique interceptado no menu Relatórios! Abrindo painel lateral...');
    openSidebarReportsPanel();
  });

  // Adicionar suporte para teclado
  newElement.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      e.stopPropagation();
      console.log('⌨️ Tecla interceptada no menu Relatórios! Abrindo painel lateral...');
      openSidebarReportsPanel();
    }
  });
  
  // Marcar como modificado
  newElement.setAttribute('data-reports-modified', 'true');
  
  // Remover submenus se existirem
  removeSubmenus(newElement);
  
  console.log('✅ Comportamento de clique modificado com sucesso');
}

function removeSubmenus(element) {
  console.log('🗑️ Removendo submenus do menu Relatórios...');

  // Encontrar container pai do menu de relatórios
  const parent = element.closest('li, .menu-item, .nav-item, .sidebar-item');
  if (parent) {
    console.log('📋 Container pai encontrado:', parent);

    // Remover submenus específicos do Chatwoot
    const submenuSelectors = [
      '.sidebar-group-children',
      'ul.sidebar-group-children',
      '.submenu',
      '.dropdown',
      'ul',
      '.menu-list',
      '[class*="sub"]',
      '[class*="children"]'
    ];

    submenuSelectors.forEach(selector => {
      const submenus = parent.querySelectorAll(selector);
      submenus.forEach(submenu => {
        if (submenu !== parent && !submenu.contains(element)) {
          console.log(`  - Removendo submenu (${selector}):`, submenu);
          submenu.style.cssText = 'display: none !important; visibility: hidden !important; height: 0 !important;';
          submenu.remove();
        }
      });
    });

    // Remover chevrons/setas específicos do Chatwoot
    const chevronSelectors = [
      '.i-lucide-chevron-up',
      '.i-lucide-chevron-down',
      '.chevron',
      '[class*="chevron"]',
      '.arrow',
      '[class*="arrow"]',
      '.caret',
      '.dropdown-toggle'
    ];

    chevronSelectors.forEach(selector => {
      const chevrons = parent.querySelectorAll(selector);
      chevrons.forEach(chevron => {
        if (!chevron.contains(element)) {
          console.log(`  - Removendo chevron (${selector}):`, chevron);
          chevron.style.cssText = 'display: none !important; visibility: hidden !important;';
        }
      });
    });

    // Remover atributos que podem expandir o menu
    parent.removeAttribute('aria-expanded');
    parent.removeAttribute('data-expanded');

    console.log('✅ Submenus removidos com sucesso');
  }
}

function openSidebarReportsPanel() {
  console.log('📋 Abrindo painel lateral de relatórios...');

  // Remover painel existente se houver
  const existingPanel = document.getElementById('sidebar-reports-panel');
  if (existingPanel) {
    existingPanel.remove();
    console.log('🗑️ Painel anterior removido');
    return;
  }

  // Criar painel lateral
  const panel = document.createElement('div');
  panel.id = 'sidebar-reports-panel';
  panel.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 200px !important;
    right: 0 !important;
    width: calc(100vw - 200px) !important;
    height: 100vh !important;
    background: white !important;
    border-left: 2px solid #e2e8f0 !important;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
    z-index: 999999 !important;
    display: flex !important;
    flex-direction: column !important;
    transform: translateX(100%) !important;
    transition: transform 0.3s ease-in-out !important;
  `;

  // Header do painel
  const header = document.createElement('div');
  header.style.cssText = `
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 16px 20px !important;
    background: #f8fafc !important;
    border-bottom: 1px solid #e2e8f0 !important;
    min-height: 60px !important;
  `;

  const title = document.createElement('div');
  title.innerHTML = `
    <span style="font-size: 20px; font-weight: 600;">📊 Relatórios Amvox</span>
    <span style="font-size: 12px; color: #64748b; margin-left: 12px;">Painel Maximizado</span>
  `;
  title.style.cssText = `
    margin: 0 !important;
    color: #1e293b !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
  `;

  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '✕';
  closeBtn.style.cssText = `
    background: none !important;
    border: none !important;
    font-size: 20px !important;
    cursor: pointer !important;
    color: #64748b !important;
    padding: 8px !important;
    border-radius: 4px !important;
    transition: all 0.2s !important;
  `;
  closeBtn.onmouseover = () => closeBtn.style.background = '#f1f5f9';
  closeBtn.onmouseout = () => closeBtn.style.background = 'none';

  // Container do iframe
  const iframeContainer = document.createElement('div');
  iframeContainer.style.cssText = `
    flex: 1 !important;
    position: relative !important;
    overflow: hidden !important;
  `;

  // Iframe
  const iframe = document.createElement('iframe');
  iframe.src = 'http://amvoxreport.aplopes.com/';
  iframe.style.cssText = `
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    background: white !important;
  `;
  iframe.setAttribute('allowfullscreen', '');
  iframe.setAttribute('allow', 'fullscreen');

  // Loading indicator
  const loading = document.createElement('div');
  loading.innerHTML = `
    <div style="display: flex; flex-direction: column; align-items: center; gap: 12px;">
      <div style="width: 40px; height: 40px; border: 4px solid #e2e8f0; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite;"></div>
      <span>Carregando relatórios...</span>
    </div>
  `;
  loading.style.cssText = `
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: #64748b !important;
    font-size: 16px !important;
    z-index: 1000000 !important;
    text-align: center !important;
  `;

  // Adicionar animação de loading
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);

  // Remover loading quando iframe carregar
  iframe.addEventListener('load', () => {
    loading.style.display = 'none';
    console.log('✅ Relatórios carregados no painel lateral');
  });

  // Eventos de fechamento
  closeBtn.addEventListener('click', () => {
    console.log('❌ Fechando painel lateral de relatórios');
    panel.style.transform = 'translateX(100%)';
    setTimeout(() => panel.remove(), 300);
  });

  // Fechar com ESC
  const escHandler = (e) => {
    if (e.key === 'Escape') {
      console.log('⌨️ ESC pressionado, fechando painel');
      panel.style.transform = 'translateX(100%)';
      setTimeout(() => panel.remove(), 300);
      document.removeEventListener('keydown', escHandler);
    }
  };
  document.addEventListener('keydown', escHandler);

  // Montar painel
  header.appendChild(title);
  header.appendChild(closeBtn);
  iframeContainer.appendChild(iframe);
  iframeContainer.appendChild(loading);
  panel.appendChild(header);
  panel.appendChild(iframeContainer);
  document.body.appendChild(panel);

  // Animar entrada
  setTimeout(() => {
    panel.style.transform = 'translateX(0)';
  }, 10);

  console.log('✅ Painel lateral de relatórios criado com sucesso');
}

function openReportsModal() {
  console.log('🖼️ Abrindo modal de relatórios...');

  // Remover modal existente se houver
  const existingModal = document.getElementById('reports-modal-custom');
  if (existingModal) {
    existingModal.remove();
  }
  
  // Criar modal
  const modal = document.createElement('div');
  modal.id = 'reports-modal-custom';
  modal.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.95) !important;
    z-index: 999999 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 20px !important;
    box-sizing: border-box !important;
  `;
  
  // Criar header
  const header = document.createElement('div');
  header.style.cssText = `
    width: 100% !important;
    max-width: 1400px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    color: white !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  `;
  
  // Título
  const title = document.createElement('h1');
  title.innerHTML = '📊 Relatório Amvox';
  title.style.cssText = `
    margin: 0 !important;
    color: white !important;
    font-size: 28px !important;
    font-weight: bold !important;
  `;
  
  // Botão fechar
  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '✕ Fechar';
  closeBtn.style.cssText = `
    background: #ff4444 !important;
    color: white !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-size: 16px !important;
    font-weight: bold !important;
    transition: background 0.3s !important;
  `;
  
  // Hover effect para o botão
  closeBtn.addEventListener('mouseenter', () => {
    closeBtn.style.background = '#cc0000 !important';
  });
  closeBtn.addEventListener('mouseleave', () => {
    closeBtn.style.background = '#ff4444 !important';
  });
  
  // Iframe
  const iframe = document.createElement('iframe');
  iframe.src = 'http://amvoxreport.aplopes.com:8080/';
  iframe.style.cssText = `
    width: 100% !important;
    height: calc(100vh - 120px) !important;
    max-width: 1400px !important;
    border: none !important;
    border-radius: 10px !important;
    background: white !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.7) !important;
  `;
  iframe.setAttribute('allowfullscreen', '');
  iframe.setAttribute('allow', 'fullscreen');
  
  // Loading indicator
  const loading = document.createElement('div');
  loading.innerHTML = '⏳ Carregando relatório...';
  loading.style.cssText = `
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    color: white !important;
    font-size: 18px !important;
    z-index: 1000000 !important;
  `;
  
  // Remover loading quando iframe carregar
  iframe.addEventListener('load', () => {
    loading.style.display = 'none';
  });
  
  // Eventos de fechamento
  closeBtn.addEventListener('click', () => {
    console.log('❌ Fechando modal de relatórios');
    modal.remove();
  });
  
  // Fechar com ESC
  const escHandler = (e) => {
    if (e.key === 'Escape' && document.getElementById('reports-modal-custom')) {
      console.log('⌨️ ESC pressionado, fechando modal');
      modal.remove();
      document.removeEventListener('keydown', escHandler);
    }
  };
  document.addEventListener('keydown', escHandler);
  
  // Fechar clicando fora
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      console.log('🖱️ Clique fora do modal, fechando');
      modal.remove();
    }
  });
  
  // Montar modal
  header.appendChild(title);
  header.appendChild(closeBtn);
  modal.appendChild(header);
  modal.appendChild(iframe);
  modal.appendChild(loading);
  document.body.appendChild(modal);
  
  console.log('✅ Modal de relatórios aberto com sucesso');
}

// Função para remover submenus de relatórios globalmente
function removeAllReportsSubmenus() {
  console.log('🧹 Removendo todos os submenus de relatórios...');

  // Seletores para submenus de relatórios
  const reportSubmenuSelectors = [
    'a[href*="/reports/"]',
    'a[href*="reports"]',
    'li[name*="Reports"]',
    '[title*="Relatórios"] + ul',
    '[name="Reports"] + ul',
    '.sidebar-group-children li a[href*="reports"]'
  ];

  reportSubmenuSelectors.forEach(selector => {
    try {
      const elements = document.querySelectorAll(selector);
      elements.forEach(el => {
        const parentLi = el.closest('li');
        if (parentLi && !parentLi.hasAttribute('data-reports-main')) {
          // Verificar se não é o menu principal de relatórios
          const text = el.textContent?.trim().toLowerCase();
          if (text !== 'relatórios' && text !== 'reports') {
            console.log(`  - Removendo submenu: ${text}`);
            parentLi.style.cssText = 'display: none !important; visibility: hidden !important;';
            parentLi.remove();
          }
        }
      });
    } catch (e) {
      // Ignorar erros
    }
  });
}

// Função para observar mudanças no DOM
function observeReportsMenu() {
  const observer = new MutationObserver((mutations) => {
    let shouldCheck = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Verificar se contém elementos relacionados a relatórios
            if (node.querySelector && (
                node.querySelector('[name="Reports"]') ||
                node.querySelector('[title="Relatórios"]') ||
                node.querySelector('a[href*="reports"]') ||
                node.textContent?.includes('Relatórios') ||
                node.textContent?.includes('Reports')
              )) {
              shouldCheck = true;
            }
          }
        });
      }
    });

    if (shouldCheck) {
      console.log('🔄 Mudanças detectadas, verificando menu de relatórios...');
      setTimeout(() => {
        modifyReportsMenu();
        removeAllReportsSubmenus();
      }, 100);
    }
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  console.log('👁️ Observer do menu de relatórios ativado');
}

// Executar quando o DOM estiver pronto
function init() {
  console.log('🚀 Iniciando modificação do menu de relatórios...');

  // Executar imediatamente
  modifyReportsMenu();
  removeAllReportsSubmenus();

  // Observar mudanças no DOM
  observeReportsMenu();

  // Re-executar periodicamente para garantir
  setInterval(() => {
    modifyReportsMenu();
    removeAllReportsSubmenus();
  }, 5000);

  // Re-executar após carregamentos
  setTimeout(() => {
    modifyReportsMenu();
    removeAllReportsSubmenus();
  }, 1000);
  setTimeout(() => {
    modifyReportsMenu();
    removeAllReportsSubmenus();
  }, 3000);
  setTimeout(() => {
    modifyReportsMenu();
    removeAllReportsSubmenus();
  }, 7000);

  console.log('✅ Sistema de modificação do menu de relatórios ativado');
}

// Executar quando o DOM estiver pronto
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}

// Executar também quando a página estiver completamente carregada
window.addEventListener('load', () => {
  setTimeout(init, 1000);
});

// Executar quando houver mudanças de rota (SPA)
window.addEventListener('popstate', () => {
  setTimeout(() => {
    modifyReportsMenu();
    removeAllReportsSubmenus();
  }, 500);
});

// Comandos globais para executar manualmente
window.modifyReportsMenu = modifyReportsMenu;
window.removeAllReportsSubmenus = removeAllReportsSubmenus;
window.openSidebarReportsPanel = openSidebarReportsPanel;

console.log('📊 Script de modificação do menu "Relatórios" carregado!');
console.log('💡 Use modifyReportsMenu() para executar manualmente.');
console.log('💡 Use removeAllReportsSubmenus() para remover submenus.');
console.log('💡 Use openSidebarReportsPanel() para abrir painel lateral.');
