# 📋 Documentação Completa do Projeto n8n-host

## 🎯 Visão Geral do Projeto

Este projeto é uma stack completa de automação e atendimento que integra:
- **N8N**: Plataforma de automação de workflows
- **Evolution API**: API para integração com WhatsApp
- **Chatwoot**: Plataforma de atendimento ao cliente
- **PostgreSQL**: Banco de dados principal
- **Redis**: Cache e filas

## 🏗️ Arquitetura dos Serviços

### Serviços Principais
```yaml
services:
  - redis: Cache e filas (porta 6379)
  - postgres: Banco principal N8N/Evolution (porta 5432)
  - n8n: Automação de workflows (porta 5678)
  - evolution: API WhatsApp (porta 8080)
  - chatwoot_postgres: Banco Chatwoot (porta 5433)
  - chatwoot_app: Aplicação Chatwoot (porta 3000)
  - chatwoot_worker: Worker Chatwoot (Sidekiq)
```

### Redes Docker
- **n8n_net**: Comunicação N8N, Redis, PostgreSQL, Chatwoot
- **evolution_net**: Comunicação Evolution API, Redis, PostgreSQL, Chatwoot

## 🎨 Customizações Implementadas

### 1. Menu Relatórios Customizado
**Arquivo**: `modify-reports-menu-final.js`
- Intercepta clique no menu "Relatórios"
- Abre modal em tela cheia com iframe
- URL: `http://amvoxreport.aplopes.com:8080/`
- Controles: ESC, clique fora, botão fechar

### 2. Remoção do Menu Capitão
**Arquivo**: `hide-captain-menu.js`
- Remove completamente o menu "Capitão" do sidebar
- Busca por texto "Capitão", "Captain" e URLs "/captain/"
- Oculta elemento e containers pais

### 3. Remoção do Help Center
**Arquivos**: 
- `force-hide-help-center.js`
- `hide-help-center-menu.js`
- Remove menus "Central de Ajuda" e "Help Center"
- Busca por URLs com 'portals', 'help', 'support', 'knowledge'

### 4. Widget Call Center
**Arquivo**: `simple-widget.js`
- Widget integrado no layout principal
- Configurável via `call_center_config.yml`
- Suporte a autenticação e permissões de mídia

## 📁 Estrutura de Arquivos

### Customizações Organizadas
```
customizations/
├── chatwoot/
│   ├── scripts/           # Scripts JavaScript
│   │   ├── modify-reports-menu-final.js
│   │   ├── hide-captain-menu.js
│   │   ├── force-hide-help-center.js
│   │   ├── hide-help-center-menu.js
│   │   └── simple-widget.js
│   ├── layouts/           # Layouts ERB
│   │   └── vueapp.html.erb
│   └── config/            # Configurações
│       └── call_center_config.yml
├── evolution-api/         # Futuras customizações
├── n8n/                  # Futuras customizações
├── README.md             # Documentação
└── INVENTORY.md          # Inventário detalhado
```

### Código Fonte Extraído
```
services/
├── chatwoot/app/         # Código fonte completo Chatwoot
├── evolution-api/        # Código fonte Evolution API
└── n8n/                 # Customizações N8N
```

### Scripts de Automação
```
apply-customizations.sh    # Aplicar customizações
backup-customizations.sh   # Backup das customizações
consolidate-customizations.sh # Consolidar customizações
extract_containers.sh      # Extrair código dos containers
```

## 🔄 Histórico de Desenvolvimento

### Commits Principais
1. **1c81bbdf** - size relatorio (atual)
2. **2a504033** - relatorio
3. **a4831d3c** - ocultar capitao
4. **831f8575** - metricas (API métricas Chatwoot)
5. **f7470dbe** - remover central de ajuda
6. **a0fc2c16** - remover capitao
7. **3576a998** - widget (Call Center Widget)
8. **648773f5** - Add chatwoot (integração inicial)

### Branch Atual
- **feature/chatwoot**: Branch de desenvolvimento das customizações
- **Status**: Working tree clean (todas as modificações commitadas)

## 🚀 Como Compartilhar e Subir as Modificações

### 1. Preparação para Deploy

#### Verificar Status Atual
```bash
git status
git log --oneline -5
```

#### Criar Branch de Release (Opcional)
```bash
git checkout -b release/chatwoot-customizations
```

### 2. Documentar Mudanças

#### Criar Release Notes
```bash
# Criar arquivo de release notes
cat > RELEASE_NOTES.md << 'EOF'
# Release: Customizações Chatwoot v1.0

## 🎯 Funcionalidades Implementadas
- Menu Relatórios customizado com iframe externo
- Remoção do menu Capitão
- Remoção do Help Center
- Widget Call Center integrado

## 🔧 Arquivos Modificados
- Layout principal: vueapp.html.erb
- 5 scripts JavaScript customizados
- Configuração Call Center

## 🚀 Deploy
1. Clonar repositório
2. Executar: docker-compose up --build
3. Ou usar: ./apply-customizations.sh
EOF
```

### 3. Preparar para Produção

#### Opção A: Push para Repositório Remoto
```bash
# Fazer push da branch atual
git push origin feature/chatwoot

# Ou criar e fazer push de uma branch de release
git checkout -b release/v1.0-chatwoot-customizations
git push origin release/v1.0-chatwoot-customizations
```

#### Opção B: Merge para Main
```bash
# Fazer merge para main (se aprovado)
git checkout main
git merge feature/chatwoot
git push origin main
```

### 4. Deploy em Novo Ambiente

#### Método 1: Clone Completo
```bash
# No servidor de destino
git clone https://github.com/seu-usuario/n8n-host.git
cd n8n-host
git checkout feature/chatwoot  # ou main se foi feito merge

# Configurar variáveis de ambiente
cp .env.example .env
# Editar .env com configurações do ambiente

# Subir serviços
docker-compose up --build -d
```

#### Método 2: Aplicar Customizações
```bash
# Se já existe um ambiente base
git pull origin feature/chatwoot
./apply-customizations.sh
```

### 5. Validação do Deploy

#### Verificar Serviços
```bash
docker-compose ps
docker-compose logs chatwoot_app
```

#### Testar Funcionalidades
1. Acessar Chatwoot: `http://localhost:3000`
2. Verificar menu Relatórios (deve abrir modal)
3. Confirmar ausência do menu Capitão
4. Confirmar ausência do Help Center
5. Verificar widget Call Center

## 📦 Pacote de Deploy

### Criar Pacote Completo
```bash
# Criar arquivo tar com tudo necessário
tar -czf n8n-host-customizations.tar.gz \
  docker-compose.yml \
  customizations/ \
  services/ \
  *.sh \
  *.md \
  config/

# Ou criar script de empacotamento
./create-deployment-package.sh
```

### Conteúdo do Pacote
- `docker-compose.yml` - Configuração dos serviços
- `customizations/` - Todas as customizações organizadas
- `services/` - Código fonte extraído (se necessário)
- Scripts de automação (`.sh`)
- Documentação completa (`.md`)
- Configurações (`config/`)

## 🔐 Configurações de Ambiente

### Variáveis Críticas
```env
# Chatwoot
CHATWOOT_SECRET_KEY_BASE=sua_chave_secreta
CHATWOOT_FRONTEND_URL=https://seu-dominio.com
CHATWOOT_POSTGRES_PASSWORD=senha_segura

# Evolution API
AUTHENTICATION_API_KEY=sua_api_key
DATABASE_CONNECTION_URI=postgresql://user:pass@host:port/db

# N8N
N8N_HOST=seu-dominio-n8n.com
WEBHOOK_URL=https://seu-dominio-n8n.com/
```

## 🛠️ Manutenção e Suporte

### Logs Importantes
```bash
# Logs do Chatwoot
docker-compose logs -f chatwoot_app

# Logs específicos das customizações
# Verificar console do navegador (F12)
```

### Troubleshooting
1. **Menu não modificado**: Verificar console para erros JS
2. **Widget não carrega**: Verificar configuração YAML
3. **Iframe não abre**: Verificar URL do relatório
4. **Customizações perdidas**: Executar `./apply-customizations.sh`

### Rollback
```bash
# Via Git
git checkout HEAD~1  # Voltar 1 commit
git checkout main     # Voltar para main

# Via backup
cp backups/docker-compose-*.backup docker-compose.yml
docker-compose up --build
```

## 📞 Próximos Passos

1. **Testar em ambiente de homologação**
2. **Criar pipeline CI/CD** para deploy automático
3. **Implementar monitoramento** das customizações
4. **Documentar procedimentos** de manutenção
5. **Treinar equipe** nas novas funcionalidades

---

**Versão**: 1.0  
**Data**: 2025-07-31  
**Autor**: Filipe Amorim  
**Status**: Pronto para produção
