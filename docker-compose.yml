services:
  redis:
    image: redis:latest
    platform: linux/amd64
    container_name: n8n_redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    environment:
      REDIS_USER: ${REDIS_USER}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "${REDIS_PORT_EXTERNAL}:${REDIS_PORT}"
    networks:
      - n8n_net
      - evolution_net
    restart: always

  postgres:
    image: postgres:latest
    platform: linux/amd64
    container_name: n8n_postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "${POSTGRES_PORT_EXTERNAL}:${POSTGRES_PORT}"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - n8n_net
      - evolution_net
    restart: always

  n8n:
    image: n8nio/n8n:latest
    platform: linux/amd64
    container_name: n8n_n8n
    restart: always
    environment:
      WEBHOOK_URL: ${WEBHOOK_URL}
      N8N_HOST: ${N8N_HOST}
      GENERIC_TIMEZONE: ${GENERIC_TIMEZONE}
      N8N_LOG_LEVEL: ${N8N_LOG_LEVEL}
      N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE: ${N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE}
      DB_TYPE: postgres
      DB_POSTGRESDB_HOST: postgres
      DB_POSTGRESDB_PORT: ${POSTGRES_PORT}
      DB_POSTGRESDB_DATABASE: ${POSTGRES_DB}
      DB_POSTGRESDB_USER: ${POSTGRES_USER}
      DB_POSTGRESDB_PASSWORD: ${POSTGRES_PASSWORD}
      QUEUE_MODE: redis
      QUEUE_REDIS_HOST: redis
      QUEUE_REDIS_PORT: ${REDIS_PORT}
      QUEUE_REDIS_DB: 0
      QUEUE_REDIS_PASSWORD: ${REDIS_PASSWORD}
    volumes:
      - n8n_data:/home/<USER>/.n8n
    ports:
      - "${N8N_PORT_EXTERNAL}:${N8N_PORT}"
    networks:
      - n8n_net

  evolution:
    container_name: n8n_evolution
    image: atendai/evolution-api:homolog
    restart: always
    depends_on:
      - redis
      - postgres
    ports:
      - "${EVOLUTION_PORT_EXTERNAL}:${EVOLUTION_PORT}"
    volumes:
      - evolution_instances:/evolution/instances
    networks:
      - evolution_net
    expose:
      - ${EVOLUTION_PORT}
    environment:
      AUTHENTICATION_API_KEY: ${AUTHENTICATION_API_KEY}
      DATABASE_ENABLED: ${DATABASE_ENABLED}
      DATABASE_PROVIDER: ${DATABASE_PROVIDER}
      DATABASE_CONNECTION_URI: ${DATABASE_CONNECTION_URI}
      DATABASE_CONNECTION_CLIENT_NAME: ${DATABASE_CONNECTION_CLIENT_NAME}
      DATABASE_SAVE_DATA_INSTANCE: ${DATABASE_SAVE_DATA_INSTANCE}
      DATABASE_SAVE_DATA_NEW_MESSAGE: ${DATABASE_SAVE_DATA_NEW_MESSAGE}
      DATABASE_SAVE_MESSAGE_UPDATE: ${DATABASE_SAVE_MESSAGE_UPDATE}
      DATABASE_SAVE_DATA_CONTACTS: ${DATABASE_SAVE_DATA_CONTACTS}
      DATABASE_SAVE_DATA_CHATS: ${DATABASE_SAVE_DATA_CHATS}
      DATABASE_SAVE_DATA_LABELS: ${DATABASE_SAVE_DATA_LABELS}
      DATABASE_SAVE_DATA_HISTORIC: ${DATABASE_SAVE_DATA_HISTORIC}
      CACHE_REDIS_URI: ${CACHE_REDIS_URI}
      CONFIG_SESSION_PHONE_VERSION: ${CONFIG_SESSION_PHONE_VERSION}

  chatwoot_postgres:
    image: ankane/pgvector
    platform: linux/amd64
    container_name: n8n_chatwoot_postgres
    environment:
      POSTGRES_USER: ${CHATWOOT_POSTGRES_USER}
      POSTGRES_PASSWORD: ${CHATWOOT_POSTGRES_PASSWORD}
      POSTGRES_DB: ${CHATWOOT_POSTGRES_DB}
    ports:
      - "${CHATWOOT_POSTGRES_PORT_EXTERNAL}:${CHATWOOT_POSTGRES_PORT}"
    volumes:
      - chatwoot_pgdata:/var/lib/postgresql/data
    networks:
      - n8n_net
    restart: always

  chatwoot_app:
    image: chatwoot/chatwoot:latest
    platform: linux/amd64
    container_name: n8n_chatwoot_app
    entrypoint: docker/entrypoints/rails.sh
    command: bundle exec rails s -p 3000 -b 0.0.0.0
    networks:
      - n8n_net
      - evolution_net
    volumes:
      - chatwoot_data:/app/storage
      - ./services/chatwoot/app/public:/app/public
      - ./services/chatwoot/app/app/views/layouts:/app/app/views/layouts
    environment:
      RAILS_ENV: production
      SECRET_KEY_BASE: ${CHATWOOT_SECRET_KEY_BASE}
      FRONTEND_URL: ${CHATWOOT_FRONTEND_URL}
      DEFAULT_LOCALE: ${CHATWOOT_DEFAULT_LOCALE}
      FORCE_SSL: ${CHATWOOT_FORCE_SSL}
      ENABLE_ACCOUNT_SIGNUP: ${CHATWOOT_ENABLE_ACCOUNT_SIGNUP}
      REDIS_URL: ${CHATWOOT_REDIS_URL}
      POSTGRES_HOST: chatwoot_postgres
      POSTGRES_DATABASE: ${CHATWOOT_POSTGRES_DB}
      POSTGRES_USERNAME: ${CHATWOOT_POSTGRES_USER}
      POSTGRES_PASSWORD: ${CHATWOOT_POSTGRES_PASSWORD}
      ACTIVE_STORAGE_SERVICE: local
      RAILS_LOG_TO_STDOUT: "true"
      USE_INBOX_AVATAR_FOR_BOT: "true"
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
      labels:
        - traefik.enable=true
        - traefik.http.routers.chatwoot_app.rule=Host(`${CHATWOOT_DOMAIN}`)
        - traefik.http.routers.chatwoot_app.entrypoints=websecure
        - traefik.http.routers.chatwoot_app.service=chatwoot_app
        - traefik.http.routers.chatwoot_app.tls.certresolver=le
        - traefik.services.chatwoot_app.loadbalancer.server.port=3000
        - traefik.http.middlewares.sslheader.headers.customrequestheaders.X-Forwarded-Proto=https
        - traefik.http.routers.chatwoot_app.middlewares=sslheader
    ports:
      - "${CHATWOOT_PORT_EXTERNAL}:3000"
    restart: always

  chatwoot_worker:
    image: chatwoot/chatwoot:latest
    platform: linux/amd64
    container_name: n8n_chatwoot_worker
    command: bundle exec sidekiq -C config/sidekiq.yml
    networks:
      - n8n_net
      - evolution_net
    volumes:
      - chatwoot_data:/app/storage
      - ./services/chatwoot/app/public:/app/public
      - ./services/chatwoot/app/app/views/layouts:/app/app/views/layouts
    environment:
      RAILS_ENV: production
      SECRET_KEY_BASE: ${CHATWOOT_SECRET_KEY_BASE}
      FRONTEND_URL: ${CHATWOOT_FRONTEND_URL}
      DEFAULT_LOCALE: ${CHATWOOT_DEFAULT_LOCALE}
      FORCE_SSL: ${CHATWOOT_FORCE_SSL}
      ENABLE_ACCOUNT_SIGNUP: ${CHATWOOT_ENABLE_ACCOUNT_SIGNUP}
      REDIS_URL: ${CHATWOOT_REDIS_URL}
      POSTGRES_HOST: chatwoot_postgres
      POSTGRES_DATABASE: ${CHATWOOT_POSTGRES_DB}
      POSTGRES_USERNAME: ${CHATWOOT_POSTGRES_USER}
      POSTGRES_PASSWORD: ${CHATWOOT_POSTGRES_PASSWORD}
      ACTIVE_STORAGE_SERVICE: local
      RAILS_LOG_TO_STDOUT: "true"
      USE_INBOX_AVATAR_FOR_BOT: "true"
    deploy:
      mode: replicated
      replicas: 1
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          cpus: "0.5"
          memory: 512M
      labels:
        - traefik.enable=true
        - traefik.http.routers.chatwoot_app.rule=Host(`${CHATWOOT_DOMAIN}`)
        - traefik.http.routers.chatwoot_app.entrypoints=websecure
        - traefik.http.routers.chatwoot_app.service=chatwoot_app
        - traefik.http.routers.chatwoot_app.tls.certresolver=le
        - traefik.services.chatwoot_app.loadbalancer.server.port=3000
        - traefik.http.middlewares.sslheader.headers.customrequestheaders.X-Forwarded-Proto=https
        - traefik.http.routers.chatwoot_app.middlewares=sslheader
    restart: always

volumes:
  pgdata:
    name: pgdata
  n8n_data:
    name: n8n_data
  evolution_instances:
    name: evolution_instances
  chatwoot_data:
    name: chatwoot_data
  chatwoot_pgdata:
    name: chatwoot_pgdata

networks:
  n8n_net:
    driver: bridge
  evolution_net:
    driver: bridge